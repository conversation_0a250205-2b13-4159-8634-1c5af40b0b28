{"validation_metrics": {"rmse_scaled": 0.03412050976489287, "mae_scaled": 0.026556451877833454, "rmse_original": 4097.692326392538, "mae_original": 3189.2890980113634, "data_period": "3_years_validation"}, "final_model_metrics": {"rmse_scaled": 0.02854541411467684, "mae_scaled": 0.022595567873188203, "rmse_original": 3428.152856140627, "mae_original": 2713.607925519891, "data_period": "complete_dataset", "mape": 3.6630978292617606, "r2_scaled": 0.9699462099260726, "r2_original": 0.9699462114749354, "test_loss": 0.0008148407796397805, "accuracy_percentage": 96.33690217073824}, "training_history": {"validation_loss": [0.0035339808091521263, 0.0009728706791065633, 0.000865876441821456, 0.0007461910718120635, 0.0006393504445441067, 0.0006000564317218959, 0.0005573525559157133, 0.0005157324485480785, 0.0005022533587180078, 0.0004564463160932064, 0.0004451506247278303, 0.00042505498277023435, 0.0004032970464322716, 0.00039931637002155185, 0.0003575460286810994, 0.00037546694511547685, 0.0003520056197885424, 0.00032348831882700324, 0.00030753150349482894, 0.0003145240480080247, 0.0003398086701054126, 0.0003084111085627228, 0.00029807479586452246, 0.0003172510187141597, 0.0003205165849067271, 0.0002808286517392844, 0.0002868416195269674, 0.00029428789275698364, 0.000296348036499694, 0.00028317648684605956, 0.00026925007114186883, 0.0002549117780290544, 0.0002790041035041213, 0.0002789708669297397, 0.00026215583784505725, 0.0002560403663665056, 0.00024472607765346766, 0.00023667413915973157, 0.00023708288790658116, 0.000245439208811149, 0.00025002899928949773, 0.00022178151994012296, 0.00022014256683178246, 0.00022321761935018003, 0.00023299366876017302, 0.00024015516100917011], "validation_val_loss": [0.002334287855774164, 0.040048204362392426, 0.002640373772010207, 0.008339897729456425, 0.0031047931406646967, 0.001246808678843081, 0.0026119251269847155, 0.007765756919980049, 0.002388692693784833, 0.003987972158938646, 0.006827959790825844, 0.004523668438196182, 0.002501330804079771, 0.0028956583701074123, 0.0031669223681092262, 0.001247407984919846, 0.0017023918917402625, 0.0022165521513670683, 0.0031729598995298147, 0.008938045240938663, 0.004040330648422241, 0.004005469847470522, 0.0015419446863234043, 0.0022086992394179106, 0.0029402989894151688, 0.0008148407796397805, 0.004927892703562975, 0.0037571380380541086, 0.0033740545623004436, 0.0018733780598267913, 0.001183076063171029, 0.0024388092570006847, 0.004264639690518379, 0.002746996469795704, 0.0023831359576433897, 0.001726128626614809, 0.001214521238580346, 0.0018308577127754688, 0.0025255985092371702, 0.00249776360578835, 0.002008059760555625, 0.0024550529196858406, 0.0019284497248008847, 0.0014042595867067575, 0.001730451243929565, 0.0035829569678753614], "epochs_trained": 46, "final_loss": 0.00024015516100917011, "final_val_loss": 0.0035829569678753614, "best_loss": 0.00022014256683178246, "best_val_loss": 0.0008148407796397805}, "model_architecture": {"window_size": 60, "features": ["price", "volume"], "lstm_layers": 3, "lstm_units": 64, "dropout_rate": 0.2, "optimizer": "Nadam", "loss_function": "mean_squared_error"}, "dataset_info": {"total_samples": 2761, "training_samples": 2148, "test_samples": 553, "train_test_split": "80/20", "date_range": {"start_date": "2018-02-05", "end_date": "2025-08-27"}}, "export_timestamp": "2025-08-28T14:16:18.815438", "model_version": "Advanced_Multivariate_LSTM_v1.0"}